fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android test

```sh
[bundle exec] fastlane android test
```

Runs all the tests

### android prodGeneral

```sh
[bundle exec] fastlane android prodGeneral
```

Upload Prod General to Google Drive

### android stagingGeneral

```sh
[bundle exec] fastlane android stagingGeneral
```

Upload Staging General to Google Drive

### android prodSumni

```sh
[bundle exec] fastlane android prodSumni
```

Upload Prod Sumni D3 Mini Apk to Google Drive

### android uploadGoogleDriveAll

```sh
[bundle exec] fastlane android uploadGoogleDriveAll
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
