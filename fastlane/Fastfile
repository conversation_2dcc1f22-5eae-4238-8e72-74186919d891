# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Upload Prod General to Google Drive"
  lane :prodGeneral do
      gradle(task: "clean assembleProdGeneralRelease")
      upload_to_google_drive(
          drive_keyfile: 'fastlane/client_key.json',
            service_account: true,
            folder_id: '1miPRoGvldrozeB_91TJLihapSAZ6_uq0',
            upload_files: ['app\build\outputs\apk\prodGeneral\release\app-prodGeneral-release.apk'],
        )
     end

    desc "Upload Staging General to Google Drive"
     lane :stagingGeneral do
         gradle(task: "clean assembleStagingGeneralDebug")
         upload_to_google_drive(
             drive_keyfile: 'fastlane/client_key.json',
             service_account: true,
             folder_id: '1miPRoGvldrozeB_91TJLihapSAZ6_uq0',
             upload_files: ['app\build\outputs\apk\stagingGeneral\debug\app-stagingGeneral-debug.apk'],
         )
      end

    desc "Upload Prod Sumni D3 Mini Apk to Google Drive"
     lane :prodSumni do
         gradle(task: "clean assembleProdSumniRelease")
         upload_to_google_drive(
             drive_keyfile: 'fastlane/client_key.json',
             service_account: true,
             folder_id: '1miPRoGvldrozeB_91TJLihapSAZ6_uq0',
             upload_files: ['app\build\outputs\apk\prodSumni\release\app-prodSumni-release.apk'],
         )
      end

  lane :uploadGoogleDriveAll do
      stagingGeneral()
      prodSumni()
  end

end
