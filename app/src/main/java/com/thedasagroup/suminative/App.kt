package com.thedasagroup.suminative

import android.app.Activity
import android.app.Application
import android.content.Intent
import android.os.Build
import android.os.Bundle 
import com.airbnb.mvrx.Mavericks
import com.instacart.truetime.time.TrueTimeImpl
import com.pluto.Pluto
import com.pluto.plugins.network.PlutoNetworkPlugin
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.service.Actions
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.ServiceState
import com.thedasagroup.suminative.ui.service.getServiceState
import com.thedasagroup.suminative.ui.service.log
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class App : Application(), Application.ActivityLifecycleCallbacks {

    @Inject
    lateinit var prefs: Prefs

    var isActivityVisible = false

    @Inject
    lateinit var trueTime: TrueTimeImpl

    override fun onCreate() {
        super.onCreate()
        Mavericks.initialize(applicationContext)
//        Pluto.showNotch(true)
        trueTime.sync()

        if(prefs.loginResponse?.user?.email != null
            && prefs.loginResponse?.user?.password != null
            && prefs.store != null){
            scheduleJob()
        }
        registerActivityLifecycleCallbacks(this)
    }


    private fun scheduleJob(){
        actionOnService(Actions.START)
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(this) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(this, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            startService(it)
        }
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityStarted(activity: Activity) {

    }

    override fun onActivityResumed(activity: Activity) {
        isActivityVisible = true
    }

    override fun onActivityPaused(activity: Activity) {
        isActivityVisible = false
    }

    override fun onActivityStopped(activity: Activity) {

    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivityDestroyed(activity: Activity) {

    }
}