package com.thedasagroup.suminative.data.prefs

import android.content.Context
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.login.Store
import com.thedasagroup.suminative.data.model.response.login.StoreSettings
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class Prefs(context: Context) {
    private val sharedPrefs = context.getSharedPreferences("prefs", Context.MODE_PRIVATE)
    var loginResponse : LoginResponse?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("loginResponse").apply()
            else {
                val json = Json.encodeToString(value)
                sharedPrefs.edit().putString("loginResponse", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("loginResponse", "") ?: ""
            if(json.isNotEmpty()) {
                val model = Json.decodeFromString<LoginResponse>(json)
                return model
            }
            return null
        }

    var store : Store?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("store").apply()
            else {
                val json = Json.encodeToString(value)
                sharedPrefs.edit().putString("store", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("store", "") ?: ""
            if(json.isNotEmpty()) {
                val model = Json.decodeFromString<Store>(json)
                return model
            }
            return null
        }

    var storeSettings : StoreSettings?
        set(value) {
            if(value == null) sharedPrefs.edit().remove("storeSettings").apply()
            else {
                val json = Json.encodeToString(value)
                sharedPrefs.edit().putString("storeSettings", json).apply()
            }
        }
        get() {
            val json = sharedPrefs.getString("storeSettings", "") ?: ""
            if(json.isNotEmpty()) {
                val model = Json.decodeFromString<StoreSettings>(json)
                return model
            }
            return null
        }

    var storeClosed : Boolean
        set(value) {
            sharedPrefs.edit().putBoolean("storeClosed", value).apply()
        }
        get() {
            return sharedPrefs.getBoolean("storeClosed", false)
        }
}

fun LoginResponse.validate(): Boolean{
    return this.user?.id != null
}
