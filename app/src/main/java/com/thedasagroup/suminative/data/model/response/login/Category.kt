package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Category(
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("createdBy")
    val createdBy: Int? = null,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("displayOrder")
    val displayOrder: Int? = null,
    @SerialName("id")
    val id: Int? = null,
    @SerialName("modifiedBy")
    val modifiedBy: Int? = null,
    @SerialName("modifiedOn")
    val modifiedOn: String? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("pic")
    val pic: String? = null,
    @SerialName("storeId")
    val storeId: Int? = null,
)