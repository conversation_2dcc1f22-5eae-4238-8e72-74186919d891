package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StoreSettings(
    @SerialName("createdBy")
    val createdBy: Int? = null,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("dRangeJson")
    val dRangeJson: List<DRangeJson>? = mutableListOf(),
    @SerialName("disabledBrandIds")
    val disabledBrandIds: List<Brand>? = mutableListOf(),
    @SerialName("disabledCategoryIds")
    val disabledCategoryIds: List<Int>?= mutableListOf(),
    @SerialName("disabledItemIds")
    val disabledItemIds: List<Int>? = mutableListOf(),
    @SerialName("id")
    val id: Int? = null,
    @SerialName("maxOrderAmount")
    val minOrderAmount: String? = null,
    @SerialName("modifiedBy")
    val modifiedBy: Int? = null,
    @SerialName("modifiedOn")
    val modifiedOn: String? = null,
    @SerialName("status")
    val status: Int? = null,
    @SerialName("storeDiscount")
    val storeDiscount: String? = null,
    @SerialName("storeId")
    val storeId: Int? = null,
    @SerialName("timingJson")
    val timingJson: List<TimingJson>? = mutableListOf(),
)