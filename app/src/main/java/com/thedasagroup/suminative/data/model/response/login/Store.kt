package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Store(
    @SerialName("address") val address: String? = null,
    @SerialName("adminCommision") val adminCommision: String? = null,
    @SerialName("banner") val banner: String? = null,
    @SerialName("businessId") val businessId: Int? = null,
    @SerialName("clientVendorId") val clientVendorId: String? = null,
    @SerialName("createdBy") val createdBy: Int? = null,
    @SerialName("createdOn") val createdOn: String? = null,
    @SerialName("defaultTax") val defaultTax: String? = null,
    @SerialName("deliveryType") val deliveryType: Int? = null,
    @SerialName("description") val description: String? = null,
    @SerialName("email") val email: String? = null,
    @SerialName("id") val id: Int? = null,
    @SerialName("isFeatured") val isFeatured: Int? = null,
    @SerialName("isRegisteredWithPandaGo") val isRegisteredWithPandaGo: Int? = null,
    @SerialName("latitude") val latitude: String? = null,
    @SerialName("longitude") val longitude: String? = null,
    @SerialName("modifiedBy") val modifiedBy: Int? = null,
    @SerialName("modifiedOn") val modifiedOn: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("pandaRegistrationDateTime") val pandaRegistrationDateTime: String? = null,
    @SerialName("phone") val phone: String? = null,
    @SerialName("pickupInstructions") val pickupInstructions: String? = null,
    @SerialName("status") val status: Int? = null,
    @SerialName("storeAdminId") val storeAdminId: Int? = null,
    @SerialName("vromoStoreId") val vromoStoreId: String? = null,
    @SerialName("prepTime") val prepareTime: Int? = 0,
)