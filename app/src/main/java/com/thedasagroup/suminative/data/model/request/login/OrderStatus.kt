package com.thedasagroup.suminative.data.model.request.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OrderStatus(
    @SerialName("id") val id: Int? = null,
    @SerialName("note") val note: String? = null,
    @SerialName("orderId") val orderId: Int? = null,
    @SerialName("status") val status: Int? = null,
    @SerialName("updatedBy") val updatedBy: Int? = null,
    @SerialName("updatedOn") val updatedOn: String? = null,
    @SerialName("stuartDelay") val stuartDelay: Int? = 0,
    @SerialName("acceptedDate") val acceptedDate: String? = null,
)