package com.thedasagroup.suminative.data.model.response.store_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class DeliveryAddress(
    @SerialName("address")
    val address: String? = null,
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("deliveryNote")
    val deliveryNote: String? = null,
    @SerialName("email")
    val email: String? = null,
    @SerialName("houseNumber")
    val houseNumber: String? = null,
    @SerialName("indexId")
    val indexId: Int? = null,
    @SerialName("latitude")
    val latitude: String? = null,
    @SerialName("longitude")
    val longitude: String? = null,
    @SerialName("personMobile")
    val personMobile: String? = null,
    @SerialName("personName")
    val personName: String? = null,
    @SerialName("pincode")
    val pincode: String? = null,
    @SerialName("streatAddress")
    val streatAddress: String? = null,
)