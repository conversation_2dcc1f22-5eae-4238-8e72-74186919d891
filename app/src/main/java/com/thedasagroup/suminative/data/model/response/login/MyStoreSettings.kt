package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class MyStoreSettings(
    @SerialName("brands")
    val brands: List<Brand>?= mutableListOf(),
    @SerialName("categories")
    val categories: List<Category>? = mutableListOf(),
    @SerialName("storeItems")
    val storeItems: List<StoreItem>? = mutableListOf(),
    @SerialName("storeSettings")
    val storeSettings: StoreSettings? = null,
    @SerialName("stores")
    val stores: List<Store>? = mutableListOf(),
    val users: List<User>? = mutableListOf()
)

@Serializable
class Brand
