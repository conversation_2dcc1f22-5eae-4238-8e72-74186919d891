package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.EDIT_STOCK
import com.thedasagroup.suminative.data.api.GET_ALL_ORDERS
import com.thedasagroup.suminative.data.api.GET_PENDING_ORDERS
import com.thedasagroup.suminative.data.api.GET_SCHEDULED_ORDERS
import com.thedasagroup.suminative.data.api.GET_STOCK_ITEMS
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest
import com.thedasagroup.suminative.data.model.request.login.OrderRequest
import com.thedasagroup.suminative.data.model.request.notification.NotificationRequest
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse
import com.thedasagroup.suminative.data.model.response.notification.NotificationResponse
import com.thedasagroup.suminative.data.model.response.stock.ChangeStockResponse
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class StockRepository : BaseRepository() {
    suspend fun getPagedStockItems(request: GetPagedStockItemsRequest): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val orderResponse2 = safeApiCall {
                val response = apiClient.post(urlString = GET_STOCK_ITEMS) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val orderResponse = when (response.status.value) {
                    200 -> {
                        Success(StockItemsResponse(
                            items = response.body<StockItemsResponse>().items ?: arrayListOf(),
                            totalCount = response.body<StockItemsResponse>().totalCount ?: 0,
                            success = true
                        ))
                    }
                    204 -> {
                        Success(StockItemsResponse(
                            items = arrayListOf(),
                            success = true
                        ))
                    }
                    else -> {
                        Success(StockItemsResponse(
                            items = arrayListOf(),
                            success = false
                        ))
                    }
                }
                return@safeApiCall orderResponse
            }
            flow.value = orderResponse2
        }
        return flow
    }

    suspend fun changeStock(request: ChangeStockRequest): StateFlow<Async<ChangeStockResponse>> {
        val flow = MutableStateFlow<Async<ChangeStockResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.patch(urlString = EDIT_STOCK) {
                    contentType(ContentType.Application.Json)
                    parameter("itemId", request.itemId)
                    parameter("stock", request.stock)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(ChangeStockResponse(
                        success = true
                    ))
                } else {
                    Success(ChangeStockResponse(
                        success = false
                    ))
                }
            }
            flow.value = response
        }
        return flow
    }
}