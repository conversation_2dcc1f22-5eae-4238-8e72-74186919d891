package com.thedasagroup.suminative.data.model.response.store_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OrderStatusHistory(
    @SerialName("id")
    val id: Int? = null,
    @SerialName("note")
    val note: String? = null,
    @SerialName("orderId")
    val orderId: Int? = null,
    @SerialName("status")
    val status: Int? = null,
    @SerialName("updatedBy")
    val updatedBy: Int? = null,
    @SerialName("updatedOn")
    val updatedOn: String? = null
)