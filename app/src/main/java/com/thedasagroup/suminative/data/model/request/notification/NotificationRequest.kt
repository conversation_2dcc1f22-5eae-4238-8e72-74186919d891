package com.thedasagroup.suminative.data.model.request.notification

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class NotificationRequest(
    @SerialName("deviceId") val deviceId: String? = null,
    @SerialName("storeId") val storeId: Int? = 0,
    @SerialName("orderId") val orderId: Int? = 0,
    @SerialName("type") val type: String? = null,
)