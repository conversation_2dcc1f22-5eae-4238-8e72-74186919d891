package com.thedasagroup.suminative.data.model.response.store_orders

import com.thedasagroup.suminative.data.model.response.login.Extra
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StoreItem(
    @SerialName("additionalInfo") val additionalInfo: String? = null,
    @SerialName("billAmount") val billAmount: Double? = null,
    @SerialName("brandId") val brandId: Int? = null,
    @SerialName("businessId") val businessId: Int? = null,
    @SerialName("categoryId") val categoryId: Int? = null,
    @SerialName("createdBy") val createdBy: Int? = null,
    @SerialName("createdOn") val createdOn: String? = null,
    @SerialName("dailyCapacity") val dailyCapacity: Int? = null,
    @SerialName("description") val description: String? = null,
    @SerialName("discountType") val discountType: Int? = null,
    @SerialName("discountedAmount") val discountedAmount: Double? = null,
    @SerialName("extras") val extras: List<Extra>? = mutableListOf(),
    @SerialName("id") val id: Int? = null,
    @SerialName("ingredients") val ingredients: String? = null,
    @SerialName("modifiedBy") val modifiedBy: Int? = null,
    @SerialName("modifiedOn") val modifiedOn: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("optionSets") val optionSets: List<OptionSet>? = mutableListOf(),
    @SerialName("pic") val pic: String? = null,
    @SerialName("preparationTime") val preparationTime: String? = null,
    @SerialName("price") val price: Double? = null,
    @SerialName("quantity") val quantity: Int? = null,
    @SerialName("servingSize") val servingSize: String? = null,
    @SerialName("storeId") val storeId: Int? = null,
    @SerialName("tax") val tax: Double? = null,
    @SerialName("unitId") val unitId: Int? = null,
)