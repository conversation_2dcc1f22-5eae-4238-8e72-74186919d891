package com.thedasagroup.suminative.ui.orders

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.StateFlow

open class GetAcceptedOrdersPagedUseCase(
    private val repo: OrdersRepository,
    private val prefs: Prefs
) {
    suspend operator fun invoke(): StateFlow<Async<OrderResponse>> {

        val orderResponse = repo.getAcceptedOrders(
            request = GetPagedOrderRequest(
                storeId = prefs.store?.id ?: 0,
            )
        )
        return orderResponse
    }
}