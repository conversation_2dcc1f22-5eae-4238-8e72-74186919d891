package com.thedasagroup.suminative.ui.orders

import android.content.Context
import android.graphics.Bitmap
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDownward
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Print
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.toMutableStateMap
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.core.view.drawToBitmap
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Order
import com.thedasagroup.suminative.ui.print.PrintingBill
import com.thedasagroup.suminative.ui.print.verticalScrollbar
import com.thedasagroup.suminative.ui.service.throttleFirst
import com.thedasagroup.suminative.ui.theme.PurpleGrey40
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.formatDate
import com.thedasagroup.suminative.ui.utils.getMinutesBetweenTwoDates
import com.thedasagroup.suminative.ui.utils.toDate
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.internal.wait
import java.net.InetAddress
import java.util.Calendar
import java.util.Date
import kotlin.math.roundToInt


val buttonWidth = 160.dp

@Composable
fun OrderScreenTopFunction(
    viewModel: OrderScreenViewModel,
    modifier: Modifier,
    callOrders: () -> Unit,
    onPrintBill: (Bitmap) -> Unit,
    onTrackingUrlClick: (String) -> Unit,
    onUpdateShowAllOrders: (Boolean) -> Unit,
) {
    val ordersResponse by viewModel.collectAsState(OrderState::ordersResponse)
    val isShowAllOrders by viewModel.collectAsState(OrderState::isShowAllOrders)

    ExpandableList(
        viewModel = viewModel,
        modifier = modifier,
        callOrders = callOrders,
        onPrintBill = onPrintBill,
        onTrackingUrlClick = onTrackingUrlClick,
        onUpdateShowAllOrders = onUpdateShowAllOrders,
        ordersResponse = ordersResponse,
        isShowAllOrders = isShowAllOrders,
        shouldShowAllOrders = true
    )
}

@Composable
fun ExpandableList(
    viewModel: OrderScreenViewModel,
    modifier: Modifier,
    callOrders: () -> Unit,
    onPrintBill: (Bitmap) -> Unit,
    onTrackingUrlClick: (String) -> Unit,
    onUpdateShowAllOrders: (Boolean) -> Unit,
    ordersResponse: Async<OrderResponse?>,
    isShowAllOrders: Boolean,
    shouldShowAllOrders: Boolean = false
) {
    val isShowPrintingPreviewDialog by viewModel.collectAsState(OrderState::isShowPrintingPreview)
    val isShowChangeStatusDialog by viewModel.collectAsState(OrderState::isShowChangeStatusDialog)
    val showAcceptOrderDelayDialog by viewModel.collectAsState(OrderState::showAcceptOrderDelayDialog)
    val currentRouteId by viewModel.collectAsState(OrderState::currentRouteId)
    val coroutineScope = rememberCoroutineScope()

    if (isShowPrintingPreviewDialog != null) {
        PrintingPreviewDialog(order = isShowPrintingPreviewDialog!!, onPrintBill = { bitmap ->
            onPrintBill(bitmap)
            viewModel.updateShowPrintingPreview(null, shouldPrintInstant = false)
        }, onCancel = {
            viewModel.updateShowPrintingPreview(null, shouldPrintInstant = false)
        }, orderScreenViewModel = viewModel
        )
    }

    if (isShowChangeStatusDialog != null) {
        viewModel.updateSelectedChangeStatusId(statusId = -1)
        ChangeStatusDialog(viewModel = viewModel,
            orderItem = isShowChangeStatusDialog!!,
            onChangeStatus = {
                viewModel.updateChangeStatusOrder(order = isShowChangeStatusDialog!!)
                coroutineScope.launch(Dispatchers.IO) {
                    viewModel.changeStatusOfOrder(
                        orderId = isShowChangeStatusDialog!!.order?.id ?: -1,
                        status = it,
                        isShowAllOrders = isShowAllOrders
                    ).collectLatest {
                        when (it) {
                            is Success -> {
                                withContext(Dispatchers.Main) {
                                    viewModel.updateChangeStatusOrder(order = null)
                                    viewModel.updateShowChangeStatusDialog(null)
                                }
                            }

                            else -> {

                            }
                        }
                    }
                }
            },
            onCancel = {
                viewModel.updateChangeStatusOrder(order = null)
                viewModel.updateShowChangeStatusDialog(order = null)
            })
    } else if (showAcceptOrderDelayDialog != null) {
        AcceptOrderWithDelayDialog(viewModel = viewModel, onAcceptOrderDelay = { order, delay ->
            coroutineScope.launch(Dispatchers.IO) {
                if (order.order?.isScheduled == false) {
                    viewModel.updateShowPrintingPreview(
                        order = order, shouldPrintInstant = true
                    )
                }
                viewModel.updateChangeStatusOrder(order = order)
                viewModel.acceptOrderWithDelay(
                    orderId = showAcceptOrderDelayDialog!!.order?.id ?: -1,
                    delayInMinutes = delay,
                    isShowAllOrders = isShowAllOrders
                ).collectLatest {
                    when (it) {
                        is Success -> {
                            withContext(Dispatchers.Main) {
                                viewModel.updateShowAcceptOrderDelayDialog(order = null)
                                viewModel.updateAcceptOrderDelay(delay = 0)
                                viewModel.updateShowPrintingPreview(
                                    order = null, shouldPrintInstant = false
                                )
                            }
                        }

                        else -> {

                        }
                    }
                }
            }
        }, onCancel = {
            viewModel.updateShowAcceptOrderDelayDialog(order = null)
            viewModel.updateAcceptOrderDelay(delay = 0)
        }, orderItem = showAcceptOrderDelayDialog!!)
    }

    when (ordersResponse) {
        is Loading -> {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(color = Color.Blue)
            }
        }

        is Success -> {
            if (ordersResponse()?.success == true && ordersResponse()?.orders?.isNotEmpty() != true) {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {}
            } else if (ordersResponse()?.success == true) {
                Column(modifier = modifier.fillMaxSize()) {
                    OrderScreen(
                        modifier = modifier,
                        ordersResponse = ordersResponse() ?: OrderResponse(),
                        viewModel = viewModel,
                        onPrintBill = { order ->
                            if (order.order?.isScheduled == false) {
                                viewModel.updateShowPrintingPreview(order = order)
                            }
                        },
                        callOrders = {
                            coroutineScope.launch(Dispatchers.IO) {
                                getCurrentRouteOrders(
                                    currentRouteId = currentRouteId,
                                    viewModel = viewModel,
                                    isShowAllOrders = isShowAllOrders
                                )
                            }
                        },
                        onUpdateShowAllOrders = {
                            onUpdateShowAllOrders(it)
                        },
                        onChangeStatus = {
                            viewModel.updateShowChangeStatusDialog(order = it)
                        },
                        onUpdateStatusSilent = { order, status, shouldPrint ->
                            if (shouldPrint && order.order?.isScheduled == false) {
                                viewModel.updateShowPrintingPreview(
                                    order = order, shouldPrintInstant = true
                                )
                            }
                            coroutineScope.launch(Dispatchers.IO) {
                                viewModel.updateClickedOrderId(order.order?.id ?: -1)
                                viewModel.changeStatusOfOrder(
                                    orderId = order.order?.id ?: -1,
                                    status = status,
                                    isShowAllOrders = isShowAllOrders
                                ).collectLatest {
                                    when (it) {
                                        is Success -> {
                                            withContext(Dispatchers.Main) {
                                                viewModel.updateShowPrintingPreview(
                                                    order = null, shouldPrintInstant = false
                                                )
                                                viewModel.updateClickedOrderId(-1)
                                            }
                                        }

                                        else -> {

                                        }
                                    }
                                }
                            }
                        },
                        onTrackingUrlClick = onTrackingUrlClick,
                        onAcceptOrderWithDelayDialog = { order ->
                            viewModel.updateShowAcceptOrderDelayDialog(order = order)
                        },
                        updateOrderMinutes = { order ->

                        },
                        shouldShowAllOrders = shouldShowAllOrders
                    )
                }
            } else {
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = if (!isNetworkConnected(LocalContext.current)) "No Internet Connection" else "Orders Load Failed",
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(modifier = Modifier.height(10.dp))
                        TextButton(
                            onClick = {
                                coroutineScope.launch(Dispatchers.IO) {
                                    getCurrentRouteOrders(
                                        currentRouteId, viewModel, isShowAllOrders
                                    )
                                }
                            },
                            modifier = Modifier
                                .background(color = Color(0xFF009551))
                                .width(100.dp)
                        ) {
                            Text(text = "Retry", color = Color.White)
                        }
                    }

                }
            }
        }

        is Fail -> {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = if (!isNetworkConnected(LocalContext.current)) "No Internet Connection" else "Orders Load Failed",
                        style = MaterialTheme.typography.bodyLarge
                    )
                    TextButton(
                        onClick = {
                            coroutineScope.launch(Dispatchers.IO) {
                                getCurrentRouteOrders(currentRouteId, viewModel, isShowAllOrders)
                            }
                        }, modifier = Modifier
                            .background(color = Color(0xFF009551))
                            .width(100.dp)
                    ) {
                        Text(text = "Retry", color = Color.White)
                    }
                }

            }
        }

        else -> {

        }
    }

}

suspend fun getCurrentRouteOrders(
    currentRouteId: String, viewModel: OrderScreenViewModel, isShowAllOrders: Boolean
) {
    if (currentRouteId == "0") {
        viewModel.getOrders(isShowAllOrders = isShowAllOrders)
    } else {
        viewModel.getScheduleOrders()
    }
}

@Composable
fun PrintingPreviewDialog(
    order: OrderItem,
    modifier: Modifier = Modifier,
    onPrintBill: (Bitmap) -> Unit,
    onCancel: () -> Unit,
    orderScreenViewModel: OrderScreenViewModel,
) {
    val shouldPrintInstant by orderScreenViewModel.collectAsState(OrderState::shouldPrintInstant)
    val screenshot = screenshotableComposable(content = {
        Column(
            modifier = Modifier
                .width(200.dp)
                .background(color = Color.White)
                .padding(8.dp)
        ) {
            PrintingBill(
                orderItem = order, orderScreenViewModel = orderScreenViewModel
            )
        }
    })

    Dialog(onDismissRequest = { onCancel() }) {
        Card(
            //shape = MaterialTheme.shapes.medium,
            shape = RoundedCornerShape(10.dp),
            // modifier = modifier.size(280.dp, 240.dp)
            modifier = Modifier.padding(10.dp, 5.dp, 10.dp, 10.dp)
        ) {
            Column(
                modifier
                    .background(Color.White)
                    .height(400.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (shouldPrintInstant) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        CircularProgressIndicator(color = Color.Blue)
                    }
                    val bitmap = screenshot.invoke()
                    onPrintBill(bitmap)
                }
                screenshot()
                Column(
                    modifier = Modifier
                        .width(200.dp)
                        .background(color = Color.White)
                        .padding(8.dp)
                        .weight(1f, fill = true)
                        .verticalScroll(rememberScrollState())
                        .verticalScrollbar(rememberScrollState())
                ) {
                    PrintingBill(orderItem = order, orderScreenViewModel = orderScreenViewModel)
                }
                if (!shouldPrintInstant) {
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(top = 10.dp)
                            .background(Color(0xFF009551)),
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {
                        TextButton(onClick = {
                            onCancel()
                        }) {
                            Text(
                                "Cancel",
                                fontWeight = FontWeight.Bold,
                                color = PurpleGrey40,
                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                            )
                        }
                        TextButton(onClick = {
                            val bitmap = screenshot.invoke()
                            onPrintBill(bitmap)
                        }) {
                            Text(
                                "Print Bill",
                                fontWeight = FontWeight.ExtraBold,
                                color = Color.Black,
                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ChangeStatusDialog(
    viewModel: OrderScreenViewModel,
    modifier: Modifier = Modifier,
    onChangeStatus: (Int) -> Unit,
    onCancel: () -> Unit,
    orderItem: OrderItem
) {
    val order = orderItem.order ?: Order()
    val selectedChangeStatusId by viewModel.collectAsState(OrderState::selectedChangeStatusId)
    val changeStatusResponse by viewModel.collectAsState(OrderState::acceptDeliveryOrderResponse)
    val changeStatusOrder by viewModel.collectAsState(OrderState::changeStatusOrder)

    Dialog(onDismissRequest = { onCancel() }) {
        Card(
            //shape = MaterialTheme.shapes.medium,
            shape = RoundedCornerShape(10.dp),
            // modifier = modifier.size(280.dp, 240.dp)
            modifier = Modifier.padding(10.dp, 5.dp, 10.dp, 10.dp)
        ) {
            if (changeStatusResponse is Loading && order.id == changeStatusOrder?.order?.id) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(color = Color.White)
                        .padding(8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    CircularProgressIndicator(color = Color.Blue)
                }
            } else {
                Column(
                    modifier.background(Color.White)
                ) {
                    Column(
                        modifier = Modifier
                            .background(color = Color.White)
                            .padding(8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Accept Order",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            Checkbox(checked = selectedChangeStatusId == 2, onCheckedChange = {
                                val status = if (it) 2 else -1
                                viewModel.updateSelectedChangeStatusId(status)
                            })
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Driver on its way",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            Checkbox(checked = selectedChangeStatusId == 9, onCheckedChange = {
                                val status = if (it) 9 else -1
                                viewModel.updateSelectedChangeStatusId(status)
                            })
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Delivered",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            Checkbox(checked = selectedChangeStatusId == 7, onCheckedChange = {
                                val status = if (it) 7 else -1
                                viewModel.updateSelectedChangeStatusId(status)
                            })
                        }
                    }
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(top = 10.dp)
                            .background(Color(0xFF009551)),
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {

                        TextButton(onClick = {
                            onCancel()
                        }) {

                            Text(
                                "Cancel",
                                fontWeight = FontWeight.Bold,
                                color = PurpleGrey40,
                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                            )
                        }
                        TextButton(onClick = {
                            onChangeStatus(selectedChangeStatusId)
                        }) {
                            Text(
                                "Ok",
                                fontWeight = FontWeight.ExtraBold,
                                color = Color.Black,
                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AcceptOrderWithDelayDialog(
    viewModel: OrderScreenViewModel,
    modifier: Modifier = Modifier,
    onAcceptOrderDelay: (OrderItem, Int) -> Unit,
    onCancel: () -> Unit,
    orderItem: OrderItem
) {
    val order = orderItem.order ?: Order()
    val acceptOrderDelay by viewModel.collectAsState(OrderState::acceptOrderDelay)
    val changeStatusResponse by viewModel.collectAsState(OrderState::acceptDeliveryOrderResponse)
    val changeStatusOrder by viewModel.collectAsState(OrderState::changeStatusOrder)

    Dialog(onDismissRequest = { onCancel() }) {
        Card(
            //shape = MaterialTheme.shapes.medium,
            shape = RoundedCornerShape(10.dp),
            // modifier = modifier.size(280.dp, 240.dp)
            modifier = Modifier.padding(10.dp, 5.dp, 10.dp, 10.dp)
        ) {
            if (changeStatusResponse is Loading && order.id == changeStatusOrder?.order?.id) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(color = Color.White)
                        .padding(8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    CircularProgressIndicator(color = Color.Blue)
                }
            } else {
                Column(
                    modifier.background(Color.White),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = "Accept Order In",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                    Column(
                        modifier = Modifier
                            .background(color = Color.White)
                            .padding(8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "15 minutes",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            Checkbox(checked = acceptOrderDelay == 15, onCheckedChange = {
                                val time = if (it) 15 else 0
                                viewModel.updateAcceptOrderDelay(delay = time)
                            })
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "30 minutes",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            Checkbox(checked = acceptOrderDelay == 30, onCheckedChange = {
                                val time = if (it) 30 else 0
                                viewModel.updateAcceptOrderDelay(delay = time)
                            })
                        }
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "35 Minutes",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Bold
                            )
                            Checkbox(checked = acceptOrderDelay == 35, onCheckedChange = {
                                val time = if (it) 35 else 0
                                viewModel.updateAcceptOrderDelay(delay = time)
                            })
                        }
                    }
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(top = 10.dp)
                            .background(Color(0xFF009551)),
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {

                        TextButton(onClick = {
                            onCancel()
                        }) {

                            Text(
                                "Cancel",
                                fontWeight = FontWeight.Bold,
                                color = PurpleGrey40,
                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                            )
                        }
                        TextButton(onClick = {
                            onAcceptOrderDelay(orderItem, acceptOrderDelay)
                        }) {
                            Text(
                                "Ok",
                                fontWeight = FontWeight.ExtraBold,
                                color = Color.Black,
                                modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun screenshotableComposable(content: @Composable () -> Unit): () -> Bitmap {
    val context = LocalContext.current
    val composeView = remember { ComposeView(context = context) }
    fun captureBitmap(): Bitmap = composeView.drawToBitmap()
    AndroidView(
        factory = {
            composeView.apply {
                setContent {
                    content()/*...content...*/
                }
            }
        },
        modifier = Modifier.wrapContentSize(unbounded = true)   //  Make sure to set unbounded true to draw beyond screen area
    )

    return ::captureBitmap
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun OrderScreen(
    ordersResponse: OrderResponse,
    modifier: Modifier,
    viewModel: OrderScreenViewModel,
    onPrintBill: (OrderItem) -> Unit,
    callOrders: () -> Unit,
    onUpdateShowAllOrders: (Boolean) -> Unit,
    onChangeStatus: (OrderItem) -> Unit,
    onUpdateStatusSilent: (OrderItem, Int, Boolean) -> Unit,
    onTrackingUrlClick: (String) -> Unit,
    onAcceptOrderWithDelayDialog: (OrderItem) -> Unit,
    updateOrderMinutes: (OrderItem) -> Unit,
    shouldShowAllOrders: Boolean = false,
) {
    val isExpandedMap = remember {
        List(
            ordersResponse.orders?.size ?: 0
        ) { index: Int -> index to false }.toMutableStateMap()
    }

    val isExpanded by viewModel.collectAsState(OrderState::isExpanded)
    val isShowAllOrders by viewModel.collectAsState(OrderState::isShowAllOrders)
    val coroutineScope = rememberCoroutineScope()
    val changeStatusResponse by viewModel.collectAsState(OrderState::changeStatusResponse)
    val acceptDeliveryOrderResponse by viewModel.collectAsState(OrderState::acceptDeliveryOrderResponse)
    val clickedOrderId by viewModel.collectAsState(OrderState::clickedOrderId)

    val function = throttleFirst<Unit>(coroutineScope = coroutineScope, skipMs = 2000) {
        coroutineScope.launch(Dispatchers.IO) {
            callOrders()
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {

        LazyVerticalStaggeredGrid(modifier = Modifier
            .padding(10.dp)
            .background(color = Color(0xFFC1C8D0)),
            columns = StaggeredGridCells.Adaptive(300.dp),
            horizontalArrangement = Arrangement.spacedBy(5.dp),
            content = {
                ordersResponse.orders?.onEachIndexed { index, orderItem ->

                    val order = orderItem.order ?: Order()

                    val bgColor = Color(0xFFC4EA98)

                    item {
                        key(order.id.toString() + order.status.toString() + order.countMinutes.toString() + order.isScheduled.toString() + order.scheduledDateTime) {
                            Card(
                                border = BorderStroke(1.dp, Color(0xFFC1C8D0)),
                                modifier = Modifier
                                    .combinedClickable(
                                        onClick = {
                                            isExpandedMap[index] = !(isExpandedMap[index] ?: true)
                                            viewModel.updateExpanded(isExpandedMap[index] ?: true)
                                        },
                                        onDoubleClick = {
                                            onUpdateStatusSilent(
                                                orderItem, 6, false
                                            )
                                        },
                                    )
                                    .fillMaxWidth(),

                                colors = CardDefaults.cardColors(
                                    containerColor = bgColor
                                )
                            ) {

                                OrderSummery(
                                    orderItem = orderItem, orderScreenViewModel = viewModel
                                )

                                Spacer(modifier = Modifier.height(10.dp))
                                HorizontalDivider(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(Color.Gray, shape = DottedShape(step = 10.dp))
                                )
                                Spacer(modifier = Modifier.height(10.dp))

                                order.getCart().forEachIndexed { index, cartItem ->
                                    Column(modifier = Modifier.padding(16.dp)) {
                                        cartItem.storeItem?.let { storeItem ->
                                            TotalCartFigma(
                                                title = "☐ ${storeItem?.quantity} x ${storeItem?.name}",
                                                isBold = true
                                            )
                                            Spacer(modifier = Modifier.height(16.dp))
                                            storeItem.extras?.forEach { element ->
                                                TotalCartFigma(
                                                    title = "☐ ${element.quantity} x ${element.name}",
                                                )
                                                Spacer(modifier = Modifier.padding(4.dp))
                                            }
                                            storeItem.optionSets?.forEach { element ->
                                                element.options?.forEach { option ->
                                                    if ((option?.quantity ?: 0) > 0) {
                                                        TotalCartFigma(
                                                            title = "☐ ${option?.quantity} x ${option?.name}",
                                                        )
                                                        Spacer(modifier = Modifier.padding(4.dp))
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                if (changeStatusResponse is Loading && clickedOrderId == order.id) {
                                    CircularProgressIndicator(
                                        Modifier.align(Alignment.CenterHorizontally),
                                        color = Color.Blue
                                    )
                                } else if (acceptDeliveryOrderResponse is Loading && clickedOrderId == order.id) {
                                    CircularProgressIndicator(
                                        Modifier.align(Alignment.CenterHorizontally),
                                        color = Color.Blue
                                    )
                                } else {
                                    when (order.status) {
                                        6 -> {
                                            Text(
                                                text = "Order # ${order.id} - Complete",
                                                color = Color.Black
                                            )
                                        }

                                        else -> {
                                            TextButton(
                                                onClick = {
                                                    onUpdateStatusSilent(
                                                        orderItem, 6, false
                                                    )
                                                },
                                                modifier = Modifier
                                                    .align(Alignment.CenterHorizontally)
                                                    .background(
                                                        color = Color(0xFF2c2c2c),
                                                        shape = RoundedCornerShape(10.dp)
                                                    )
                                                    .width(160.dp)
                                            ) {
                                                Text(
                                                    text = "Complete", color = Color.White
                                                )
                                            }
                                            Spacer(modifier = Modifier.height(10.dp))
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
            })
    }
}

@Composable
fun OrderSummery(orderItem: OrderItem, orderScreenViewModel: OrderScreenViewModel) {
    val storeSettingsResponse by orderScreenViewModel.collectAsState(OrderState::storeSettingsResponse)
    val prepTime = when (storeSettingsResponse) {
        is Success -> storeSettingsResponse()?.store?.prepareTime
        else -> 0
    }
    
    // State to trigger recomposition every minute
    var currentTimeMillis by remember { mutableStateOf(System.currentTimeMillis()) }
    
    // Update current time every minute
    LaunchedEffect(Unit) {
        while (true) {
            currentTimeMillis = System.currentTimeMillis()
            delay(60000) // Update every minute
        }
    }
    
    val order = orderItem.order ?: Order()
    val createdDate = order.createdOn?.toDate(
        DATE_FORMAT_BACK_END
    ) ?: Date()
    val prepMillis = createdDate.time + (prepTime?.times(60) ?: 0).times(1000)
    // Use current time for calculation (currentTimeMillis ensures recomposition)
    val currentDate = Date(currentTimeMillis)
    val minutes = getMinutesBetweenTwoDates(
        startDate = currentDate,
        endDate = Date(prepMillis)
    )
    val customer = orderItem.customer
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(5.dp)
    ) {
        Spacer(modifier = Modifier.height(10.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.width(180.dp),
                text = "${orderItem.customer?.name ?: "WalkIn Customer"} # ${order.id.toString()}",
                style = TextStyle(
                    fontFamily = fontNunito,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                ),
                fontWeight = FontWeight.Bold
            )
        }
        Spacer(modifier = Modifier.height(10.dp))
        HorizontalDivider(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.Gray, shape = DottedShape(step = 10.dp))
        )
        Spacer(modifier = Modifier.height(10.dp))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            //image
            Image(
                painter = painterResource(R.drawable.dasa_logo),
                contentDescription = "Order Image",
                modifier = Modifier.size(40.dp)
            )

            Spacer(modifier = Modifier.height(10.dp))

            val deliveryType = when (order.deliveryType) {
                1 -> "Pickup"
                2 -> "Delivery"
                else -> "In-Store"
            }

            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth(0.7f)
            ) {

                Text(
                    modifier = Modifier
                        .background(
                            color = Color(0xFFb5b01f), RoundedCornerShape(10.dp)
                        )
                        .padding(5.dp),
                    text = deliveryType,
                    color = Color.White,
                    style = TextStyle(
                        fontFamily = fontPoppins, fontSize = 14.sp
                    )
                )

                if (minutes > 0) {
                    Text(
                        text = "Due in ${minutes} mins", style = TextStyle(
                            fontFamily = fontNunito, fontSize = 14.sp, fontWeight = FontWeight.Bold
                        )
                    )
                } else {
                    Text(
                        text = "Overdue by ${minutes * -1} mins", style = TextStyle(
                            fontFamily = fontNunito, fontSize = 14.sp, fontWeight = FontWeight.Bold
                        )
                    )
                }
            }
            IconButton(
                onClick = {},
                modifier = Modifier
                    .background(color = Color(0xFFec221f))
                    .padding(5.dp),
            ) {
                Icon(
                    tint = Color.White, imageVector = Icons.Default.Print, contentDescription = null
                )
            }
        }
    }
}


fun isNetworkConnected(context: Context): Boolean {
    try {
        val ipAddr = InetAddress.getByName("google.com")

        //You can replace it with your name
        return ipAddr.toString() != ""
    } catch (e: Exception) {
        return false
    }
}


@Composable
fun TotalCart(
    modifier: Modifier = Modifier,
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.bodySmall,
    isBold: Boolean = false
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            modifier = Modifier.width(30.sdp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
        Text(
            modifier = Modifier.width(30.sdp),
            text = value,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun ItemText(
    modifier: Modifier = Modifier,
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.bodySmall,
    isBold: Boolean = false
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            modifier = Modifier.width(60.sdp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun TotalCartFigma(
    title: String, style: TextStyle = TextStyle(
        fontFamily = fontNunito,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal,
    ), isBold: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            modifier = Modifier.width(180.dp),
            text = title,
            style = style,
            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
        )
    }
}

private data class DottedShape(
    val step: Dp,
) : Shape {
    override fun createOutline(
        size: Size, layoutDirection: LayoutDirection, density: Density
    ) = Outline.Generic(Path().apply {
        val stepPx = with(density) { step.toPx() }
        val stepsCount = (size.width / stepPx).roundToInt()
        val actualStep = size.width / stepsCount
        val dotSize = Size(width = actualStep / 2, height = size.height)
        for (i in 0 until stepsCount) {
            addRect(
                Rect(
                    offset = Offset(x = i * actualStep, y = 0f), size = dotSize
                )
            )
        }
        close()
    })
}