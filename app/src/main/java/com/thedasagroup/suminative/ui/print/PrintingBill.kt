package com.thedasagroup.suminative.ui.print

import android.annotation.SuppressLint
import android.graphics.Picture
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.LinearLayout
import android.widget.ScrollView
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.draw
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.airbnb.mvrx.compose.mavericksViewModel
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.response.store_orders.Customer
import com.thedasagroup.suminative.data.model.response.store_orders.Order
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.SunmiPrintHelper
import com.thedasagroup.suminative.ui.utils.formatDate
import com.thedasagroup.suminative.ui.utils.transformDecimal
import kotlin.math.max

@Composable
fun PrintingBill(orderItem: OrderItem, orderScreenViewModel: OrderScreenViewModel) {
    val order = orderItem.order ?: Order()
    Text(text = "Dasa Direct", style = MaterialTheme.typography.bodyLarge)
    Spacer(modifier = Modifier.padding(4.dp))
    Column(
        modifier = Modifier
            .padding(2.dp)
            .clip(shape = RoundedCornerShape(10.dp))
            .background(color = Color.Black)
            .padding(10.dp)
            .fillMaxWidth()
    ) {
        val firstName = orderItem.customer?.name?.split(" ")?.firstOrNull()
            ?: orderItem.customer?.name
        Text(
            text = firstName ?: "",
            style = MaterialTheme.typography.bodyLarge,
            color = Color.White
        )
        Text(
            text = order.id?.formatOrderId() ?: "",
            style = MaterialTheme.typography.bodyLarge,
            color = Color.White
        )
    }

    Text(
        text = if (order.deliveryType == 2) "Delivery" else "Pick Up",
        style = MaterialTheme.typography.bodyLarge
    )
    Spacer(modifier = Modifier.padding(4.dp))
    MyTextDivider()
    /*Spacer(modifier = Modifier.padding(4.dp))
    Text(
        text = "Customer note",
        style = MaterialTheme.typography.bodySmall,
        color = Color.Black
    )

    Text(text = order.deliveryNote ?: "", style = MaterialTheme.typography.bodySmall)*/
//    Spacer(modifier = Modifier.padding(4.dp))
//    MyTextDivider()
    Spacer(modifier = Modifier.padding(4.dp))
    Text(text = "${order.getCart().size} Items", style = MaterialTheme.typography.bodyMedium)
    Spacer(modifier = Modifier.padding(4.dp))
    order.getCart().forEach { item ->
        Total(
            title = "☐ ${item.storeItem?.quantity} x ${item.storeItem?.name}",
            value = "£ ${((item.storeItem?.price ?: 0.0) * (item.storeItem?.quantity ?: 0)).transformDecimal()}",
            isBold = true,
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(modifier = Modifier.padding(4.dp))
        item.storeItem?.extras?.forEach { element ->
            Total(
                title = "☐ ${element.quantity} x ${element.name}",
                value = "£ ${((item.storeItem.price ?: 0.0) * (element.quantity ?: 0)).transformDecimal()}",
            )
            Spacer(modifier = Modifier.padding(4.dp))
        }
        item.storeItem?.optionSets?.forEach { element ->
            element.options?.forEach { option ->
                if ((option.quantity ?: 0) > 0) {
                    Total(
                        title = "☐ ${option.quantity} x ${option.name}",
                        value = "£ ${((option.price ?: 0.0) * (option.quantity ?: 0) * (item.storeItem.quantity ?: 0)).transformDecimal()}"
                    )
                    Spacer(modifier = Modifier.padding(4.dp))
                }
            }
        }
    }
    MyTextDivider()
    Spacer(modifier = Modifier.padding(4.dp))
    Total(
        title = "Sub Total",
        value = "£ ${((order.totalPrice ?: 0.0) + (order.totalOptionPrice ?: 0.0)).transformDecimal()}",
        style = MaterialTheme.typography.bodyMedium
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total(
        title = "Tax",
        value = "£ ${(order.tax ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.bodyMedium
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total(
        title = "Delivery Fee",
        value = "£ ${(order.deliveryCharges ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.bodyMedium
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total(
        title = "Discount",
        value = "£ ${(order.discountOnPromo ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.bodyMedium
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total(
        title = "Total",
        value = "£ ${(order.netPayable ?: 0.0).transformDecimal()}",
        style = MaterialTheme.typography.bodyMedium
    )

    MyTextDivider()
    DateComposable(
        title = "Placed",
        value = "${order.createdOn?.formatDate(DATE_FORMAT_BACK_END)}",
        style = MaterialTheme.typography.bodyMedium
    )

    Spacer(modifier = Modifier.padding(4.dp))
    /*if (order.deliveryType != 2) {
        DateComposable(
            title = "Picked Up",
            value = "${order.pickupTime}",
            style = MaterialTheme.typography.bodyMedium
        )
        Spacer(modifier = Modifier.padding(4.dp))
    }*/
    MyTextDivider()

}

@Composable
fun MyTextDivider() {
    Text(
        maxLines = 1,
        text = "----------------------------------------------",
        style = MaterialTheme.typography.bodySmall
    )
}

@Composable
fun Total(title: String, value: String, style: TextStyle = MaterialTheme.typography.bodySmall, isBold : Boolean = false) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(modifier = Modifier.width(100.dp), text = title, style = style, fontWeight = if(isBold) FontWeight.Bold else FontWeight.Normal)
        Text(modifier = Modifier.width(60.dp), text = value, style = style, fontWeight = if(isBold) FontWeight.Bold else FontWeight.Normal)
    }
}

@Composable
fun DateComposable(title: String, value: String, style: TextStyle = MaterialTheme.typography.bodySmall, isBold : Boolean = false) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(modifier = Modifier.width(80.dp), text = title, style = style, fontWeight = if(isBold) FontWeight.Bold else FontWeight.Normal)
        Text(modifier = Modifier.width(100.dp), text = value, style = style, fontWeight = if(isBold) FontWeight.Bold else FontWeight.Normal)
    }
}


@SuppressLint("DefaultLocale")
fun Int.formatOrderId(): String {
    return String.format("%05d", this)
}

//Composeable preview
/*@Composable
@Preview
fun PrintingBillPreview() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.White)
    ) {
        PrintingBill(
            orderItem = Order(
                customer = Customer(
                    name = "Sheeraz"
                ), id = 1,
            ),
            orderScreenViewModel = mavericksViewModel())
    }
}*/

fun Modifier.verticalScrollbar(
    scrollState: ScrollState,
    scrollBarWidth: Dp = 4.dp,
    minScrollBarHeight: Dp = 5.dp,
    scrollBarColor: Color = Color.Blue,
    cornerRadius: Dp = 2.dp
): Modifier = composed {
    val targetAlpha = if (scrollState.isScrollInProgress) 1f else 0f
    val duration = if (scrollState.isScrollInProgress) 150 else 500

    val alpha by animateFloatAsState(
        targetValue = targetAlpha,
        animationSpec = tween(durationMillis = duration)
    )

    drawWithContent {
        drawContent()

        val needDrawScrollbar = scrollState.isScrollInProgress || alpha > 0.0f

        if (needDrawScrollbar && scrollState.maxValue > 0) {
            val visibleHeight: Float = this.size.height - scrollState.maxValue
            val scrollBarHeight: Float = max(visibleHeight * (visibleHeight / this.size.height), minScrollBarHeight.toPx())
            val scrollPercent: Float = scrollState.value.toFloat() / scrollState.maxValue
            val scrollBarOffsetY: Float = scrollState.value + (visibleHeight - scrollBarHeight) * scrollPercent

            drawRoundRect(
                color = scrollBarColor,
                topLeft = Offset(this.size.width - scrollBarWidth.toPx(), scrollBarOffsetY),
                size = Size(scrollBarWidth.toPx(), scrollBarHeight),
                alpha = alpha,
                cornerRadius = CornerRadius(cornerRadius.toPx())
            )
        }
    }
}

@Composable
fun DrawScrollableView(content: @Composable () -> Unit, modifier: Modifier) {
    AndroidView(
        modifier = modifier,
        factory = {
            val scrollView = ScrollView(it)
            val layout = LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT)
            scrollView.layoutParams = layout
            scrollView.isVerticalFadingEdgeEnabled = true
            scrollView.isScrollbarFadingEnabled = false
            scrollView.addView(ComposeView(it).apply {
                setContent {
                    content()
                }
            })
            val linearLayout = LinearLayout(it)
            linearLayout.orientation = LinearLayout.VERTICAL
            linearLayout.layoutParams = LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT)
            linearLayout.addView(scrollView)
            linearLayout
        }
    )
}