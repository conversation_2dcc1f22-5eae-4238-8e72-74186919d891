package com.thedasagroup.suminative.ui.orders

import android.icu.util.Calendar
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest
import com.thedasagroup.suminative.data.model.request.login.BankDetail
import com.thedasagroup.suminative.data.model.request.login.Brand
import com.thedasagroup.suminative.data.model.request.login.Business
import com.thedasagroup.suminative.data.model.request.login.Category
import com.thedasagroup.suminative.data.model.request.login.ChangePassword
import com.thedasagroup.suminative.data.model.request.login.Cms
import com.thedasagroup.suminative.data.model.request.login.Conversation
import com.thedasagroup.suminative.data.model.request.login.Customer
import com.thedasagroup.suminative.data.model.request.login.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.login.DeliverySettingRange
import com.thedasagroup.suminative.data.model.request.login.DeliverySettings
import com.thedasagroup.suminative.data.model.request.login.Extra
import com.thedasagroup.suminative.data.model.request.login.ExtraItemRelation
import com.thedasagroup.suminative.data.model.request.login.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.login.Option
import com.thedasagroup.suminative.data.model.request.login.OptionSet
import com.thedasagroup.suminative.data.model.request.login.Order
import com.thedasagroup.suminative.data.model.request.login.OrderStatus
import com.thedasagroup.suminative.data.model.request.login.PaymentData
import com.thedasagroup.suminative.data.model.request.login.PromoCodes
import com.thedasagroup.suminative.data.model.request.login.Store
import com.thedasagroup.suminative.data.model.request.login.StoreItem
import com.thedasagroup.suminative.data.model.request.login.StoreSetting
import com.thedasagroup.suminative.data.model.request.login.SupportDetail
import com.thedasagroup.suminative.data.model.request.login.UiSettings
import com.thedasagroup.suminative.data.model.request.login.User
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_APP
import com.thedasagroup.suminative.ui.utils.formatDate
import com.thedasagroup.suminative.ui.utils.toDate
import com.thedasagroup.suminative.ui.utils.toGMT
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class AcceptOrderWithDelayUseCase(
    private val repo: OrdersRepository, private val prefs: Prefs,
    private val trueTimeImpl: TrueTimeImpl,
    private val getStoreSettingsUseCase: GetStoreSettingsUseCase
) : GetPendingOrdersPagedUseCase(repo, prefs) {
    suspend operator fun invoke(
        orderId: Int, isShowAllOrder: Boolean, delayInMinutes: Int,
    ): StateFlow<Async<OrderResponse?>> {
        val now = trueTimeImpl.now().toGMT()
        val calenderNow = Calendar.getInstance()
        calenderNow.time = now
        val prepTime = getStoreSettingsUseCase().value()?.myStoreSettings?.stores?.firstOrNull { it.id == prefs.store?.id }?.prepareTime ?: 0
        val updatedDelay = if(prepTime > 0){
            delayInMinutes + prepTime
        }
        else {
            delayInMinutes
        }
        calenderNow.add(Calendar.MINUTE,  updatedDelay)
        val acceptedDate = calenderNow.time.formatDate(DATE_FORMAT_APP)

        val changeStatusResponse = repo.changeStatus(
            request = ChangeStatusRequest(
                storeAdminId = -1,
                email = "",
                phoneNumber = "",
                storeId = prefs.store?.id ?: 105,
                storeSetting = StoreSetting(),
                cms = Cms(),
                order = Order(),
                bankDetail = BankDetail(),
                brand = Brand(),
                category = Category(),
                storeItem = StoreItem(),
                extra = Extra(),
                extraItemRelation = ExtraItemRelation(),
                appType = "cms",
                startDate = "",
                endDate = "",
                deliveryAddress = DeliveryAddress(),
                feedbackComplain = FeedbackComplain(),
                conversation = Conversation(),
                orderStatus = OrderStatus(
                    id = -1,
                    note = "",
                    orderId = orderId,
                    status = 2,
                    updatedBy = prefs.loginResponse?.user?.id ?: -1,
                    updatedOn = "",
                    acceptedDate = acceptedDate,
                    stuartDelay = delayInMinutes
                ),
                paymentData = PaymentData(),
                customer = Customer(),
                orderId = -1,
                pandaOrderId = "",
                optionSet = OptionSet(),
                option = Option(),
                optionSetId = -1,
                deliverySettingId = -1,
                deliverySettings = DeliverySettings(),
                deliverySettingRangeId = -1,
                deliverySettingRange = DeliverySettingRange(),
                uiSettings = UiSettings(),
                command = "changeStatusOfAnOrder",
                user = User(),
                business = Business(),
                loggedUserId = -1,
                supportDetail = SupportDetail(),
                changePassword = ChangePassword(),
                businessId = -1,
                store = Store(),
                promoCodes = PromoCodes(),
            )
        )

        val ordersResponse = when (changeStatusResponse.value) {
            is Success -> {
                val order = this.invoke(isShowAllOrder = isShowAllOrder)
                when (order.value) {
                    is Success -> {
                        Success(order.value())
                    }

                    else -> {
                        order.value
                    }
                }
            }

            is Loading -> {
                Loading()
            }

            is Fail -> {
                Fail<OrderResponse>((changeStatusResponse.value as Fail<ChangeStatusResponse>).error)
            }

            else -> {
                Uninitialized
            }
        }
        return MutableStateFlow(ordersResponse)
    }
}