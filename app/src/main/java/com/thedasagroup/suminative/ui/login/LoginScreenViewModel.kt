package com.thedasagroup.suminative.ui.login

import android.security.keystore.KeyProperties
import android.util.Base64
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher

class LoginScreenViewModel @AssistedInject constructor(
    @Assisted state: LoginScreenState, val loginUseCase: LoginUseCase, val prefs: Prefs
) : MavericksViewModel<LoginScreenState>(state) {
    fun updateEmail(email: String) {
        setState {
            copy(email = email)
        }
    }

    fun updatePassword(password: String) {
        setState { copy(password = password) }
    }

    suspend fun login(email: String, password: String): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        setState {
            copy(loginResponse = Loading())
        }
        loginUseCase(
            email = email, password = password
        ).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(loginResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(loginResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun updateLoginError(error : String){
        setState {
            copy(loginError = error)
        }
    }

    fun updateEmailError(error : String){
        setState {
            copy(emailError = error)
        }
    }
    fun updatePasswordError(error : String){
        setState {
            copy(passwordError = error)
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<LoginScreenViewModel, LoginScreenState> {
        override fun create(state: LoginScreenState): LoginScreenViewModel
    }

    companion object :
        MavericksViewModelFactory<LoginScreenViewModel, LoginScreenState> by hiltMavericksViewModelFactory()
}

data class LoginScreenState(
    val email: String = "",
    val password: String = "",
    val loginResponse: Async<LoginResponse> = Uninitialized,
    val loginError : String = "",
    val emailError : String = "",
    val passwordError : String = ""
) : MavericksState

fun decodePublicKey(publicKey: String?): PublicKey {
    val decodedKey = Base64.decode(publicKey, Base64.DEFAULT)
    // Convert the byte array back to PublicKey object
    val keyFactory = KeyFactory.getInstance(KeyProperties.KEY_ALGORITHM_RSA)
    val keySpec = X509EncodedKeySpec(decodedKey)
    return keyFactory.generatePublic(keySpec)
}

fun encrypt(data: String, publicKey: PublicKey): String {
    val cipher: Cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
    cipher.init(Cipher.ENCRYPT_MODE, publicKey)
    val bytes = cipher.doFinal(data.toByteArray())
    return Base64.encodeToString(bytes, Base64.DEFAULT)
}