package com.thedasagroup.suminative.ui.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.Network
import android.os.Build
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.Worker
import androidx.work.WorkerParameters
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow


class NetworkChangeWorker(context: Context, workerParams: WorkerParameters) : Worker(context, workerParams) {
    override fun doWork(): Result {
        actionOnService(Actions.START)
        sendMessage(message = "Network Change", context = applicationContext, type = "Network")
        return Result.success()
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(applicationContext) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(applicationContext, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                applicationContext.startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            applicationContext.startService(it)
        }
    }

    private fun sendMessage(message : String, type : String, context: Context) {
        val intent = Intent("custom-event-name")
        // You can also include some extra data.
        intent.putExtra("message", message)
        intent.putExtra("type", type)
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent)
    }
}