package com.thedasagroup.suminative.ui.login

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.EncryptedNdk
import com.thedasagroup.suminative.data.model.request.login2.LoginRequest2
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import kotlinx.coroutines.flow.StateFlow

class LoginUseCase(private val loginRepository: LoginRepository) {
    suspend operator fun invoke(email: String, password: String): StateFlow<Async<LoginResponse>> {

        val publicKey = decodePublicKey(BuildConfig.APITOKEN)
        val encryptedPassword = encrypt(password, publicKey)

        return loginRepository.login(
            loginRequest = LoginRequest2(
                email = email, password = encryptedPassword.replace("\n", "")
            )
        )
    }
}