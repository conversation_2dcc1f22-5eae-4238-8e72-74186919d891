package com.thedasagroup.suminative.ui.login

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.store_settings.StoreSettingsRequest
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.login.StoreSettings
import com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import kotlinx.coroutines.flow.StateFlow

open class GetStoreSettingsUseCase(private val loginRepository: LoginRepository, private val prefs : Prefs) {
    suspend operator fun invoke() : StateFlow<Async<StoreSettingsResponse>> {

        return loginRepository.getStoreSettings(
            storeSettingsRequest = StoreSettingsRequest(
                storeId = prefs.store?.id,
                businessId = prefs.loginResponse?.businesses?.id ?: 0,
                command = "getMySoreSettingOk",
            )
        )
    }
}