package com.thedasagroup.suminative.ui.stock

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.ScrollableTabRow
import androidx.compose.material.TabPosition
import androidx.compose.material.TabRow
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.outlined.Home
import androidx.compose.material.icons.outlined.Settings
import androidx.compose.material.icons.outlined.ShoppingCart
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.UiComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.compose.rememberNavController
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.ui.orders.buttonWidth
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@OptIn(ExperimentalFoundationApi::class, ExperimentalMaterial3Api::class)
@Composable
fun StockScreen(viewModel: StockScreenViewModel, onBackClick: () -> Unit) {
    SumiNativeTheme {
        val navController = rememberNavController()
        val coroutineScope = rememberCoroutineScope()
        Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
            val showStockItemDialog by viewModel.collectAsState(StockScreenState::showUpdateStockDialog)
            if (showStockItemDialog != null) {
                ChangeStockDialog(viewModel = viewModel, onCancel = {
                    viewModel.showUpdateStockDialog(stockItem = null)
                }, stockItem = showStockItemDialog!!, onUpdateStock = { stockItem, stock ->
                    coroutineScope.launch(Dispatchers.IO) {
                        viewModel.changeStock(ChangeStockRequest(stockItem.id ?: 0, stock))
                            .collectLatest { response ->
                                when (response) {
                                    is Success -> {
                                        viewModel.showUpdateStockDialog(stockItem = null)
                                    }

                                    else -> {}
                                }
                            }
                    }
                })
            }
            tabRow(viewModel = viewModel, onClickUpdateStock = { stockItem ->
                viewModel.showUpdateStockDialog(stockItem = stockItem)
            }, onBackClick = onBackClick)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@ExperimentalFoundationApi
@Composable
fun tabRow(
    viewModel: StockScreenViewModel,
    onClickUpdateStock: (StockItem) -> Unit,
    onBackClick: () -> Unit
) {
    val response by viewModel.collectAsState(StockScreenState::stockItemsResponse)
    if (response is Loading || response is Uninitialized) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator(color = Color.Blue)
        }
    } else {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TopAppBar(title = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back Button
                    IconButton(onClick = {
                        onBackClick()
                    }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack, contentDescription = "Back"
                        )
                    }
                    Spacer(modifier = Modifier.size(8.sdp))
                    Text(text = "Stock Management", style = MaterialTheme.typography.bodyLarge)
                }
            })

            val tabItem = response()?.mapCategories?.keys?.map {
                TabItem(title = it ?: "")
            } ?: emptyList()

            var selectedTabIndex by remember {
                mutableIntStateOf(0)
            }

            val pagerState = rememberPagerState {
                tabItem.size
            }

            if (tabItem.size > 0) {

                LaunchedEffect(key1 = selectedTabIndex) {
                    pagerState.animateScrollToPage(selectedTabIndex)
                }

                LaunchedEffect(key1 = pagerState.currentPage, pagerState.isScrollInProgress) {
                    if (!pagerState.isScrollInProgress) selectedTabIndex = pagerState.currentPage
                }

                ScrollableTabRow(selectedTabIndex = selectedTabIndex) {
                    tabItem.forEachIndexed { index, tabItem ->

                        androidx.compose.material3.Tab(selected = index == selectedTabIndex,
                            onClick = {
                                selectedTabIndex = index
                            },
                            text = { Text(text = tabItem.title, color = Color.White) })
                    }
                }

                HorizontalPager(
                    state = pagerState, modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) { index ->
                    Box(modifier = Modifier.fillMaxSize()) {
                        LazyColumn {
                            items(
                                items = response()?.mapCategories?.get(tabItem[index].title)
                                    ?: emptyList()
                            ) { item ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(8.sdp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column(modifier = Modifier
                                        .width(200.sdp)
                                        .padding(8.sdp)
                                        .clickable {
                                            onClickUpdateStock(item)
                                        }) {
                                        Text(
                                            item.name ?: "",
                                            style = MaterialTheme.typography.headlineSmall
                                        )
                                        Spacer(modifier = Modifier.padding(4.sdp))
                                        Text(
                                            item.description ?: "",
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                        Spacer(modifier = Modifier.padding(4.sdp))
                                        Text("Stock: ${getStockString(item.stock ?: 0)}")
                                    }
                                    Spacer(modifier = Modifier.padding(8.sdp))
                                    TextButton(
                                        onClick = {
                                            onClickUpdateStock(item)
                                        },
                                        modifier = Modifier
                                            .background(color = Color(0xFF009551))
                                            .width(buttonWidth)
                                    ) {
                                        androidx.compose.material3.Text(
                                            text = "Update Stock", color = Color.White
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

fun getStockString(stock: Int): String {
    return when (stock) {
        1 -> "In Stock"
        2 -> "Sold out for Today"
        else -> "Off The Menu"
    }
}

// Data Class to handle items
data class TabItem(
    val title: String
)