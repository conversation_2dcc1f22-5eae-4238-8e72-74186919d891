package com.thedasagroup.suminative.ui.tracking

import android.os.Bundle
import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatButton
import com.thedasagroup.suminative.R

class TrackingActivity : AppCompatActivity() {

    companion object{
        const val TRACKING_URL = "TRACKING_URL"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_tracking)
        val webView = findViewById<WebView>(R.id.webview)
        val btnBack = findViewById<AppCompatButton>(R.id.btnBack)
        btnBack.setOnClickListener {
            finish()
        }
        val trackingUrl = intent.getStringExtra(TRACKING_URL)
        webView.loadUrl(trackingUrl!!)
    }
}