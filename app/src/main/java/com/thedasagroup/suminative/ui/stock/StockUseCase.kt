package com.thedasagroup.suminative.ui.stock

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import kotlinx.coroutines.flow.StateFlow

open class StockUseCase(private val stockRepository: StockRepository, private val prefs: Prefs) {
    suspend operator fun invoke() : StateFlow<Async<StockItemsResponse>> {

        return stockRepository.getPagedStockItems(
            request = GetPagedStockItemsRequest(storeId = prefs.store?.id ?: 0)
        )
    }
}