package com.thedasagroup.suminative.ui.login

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ElevatedCard
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import coil.size.Scale
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.validate
import com.thedasagroup.suminative.ui.common.customComposableViews.EmailTextField
import com.thedasagroup.suminative.ui.common.customComposableViews.MediumTitleText
import com.thedasagroup.suminative.ui.common.customComposableViews.NormalButton
import com.thedasagroup.suminative.ui.common.customComposableViews.PasswordTextField
import com.thedasagroup.suminative.ui.common.customComposableViews.TitleText
import com.thedasagroup.suminative.ui.orders.isNetworkConnected
import com.thedasagroup.suminative.ui.theme.AppTheme
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Composable
fun LoginScreen(
    loginViewModel: LoginScreenViewModel, onLoginSuccess: () -> Unit, onLoginError: (String) -> Unit
) {

    val coroutineScope = rememberCoroutineScope()

    val email: String by loginViewModel.collectAsState(LoginScreenState::email)
    val password: String by loginViewModel.collectAsState(LoginScreenState::password)
    val loginResponse by loginViewModel.collectAsState(LoginScreenState::loginResponse)
    val emailError by loginViewModel.collectAsState(LoginScreenState::emailError)
    val passwordError by loginViewModel.collectAsState(LoginScreenState::passwordError)

    val emailValue = remember {
        mutableStateOf(TextFieldValue(email))
    }

    val passwordValue = remember {
        mutableStateOf(TextFieldValue(password))
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .navigationBarsPadding()
            .imePadding()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // Main card Content for Login
        ElevatedCard(
            modifier = Modifier
                .background(color = Color(0xFFC1C8D0))
                .fillMaxWidth()
                .padding(50.sdp)
        ) {
            Column(
                modifier = Modifier
                    .background(color = Color.White)
                    .padding(horizontal = AppTheme.dimens.paddingLarge)
                    .padding(bottom = AppTheme.dimens.paddingExtraLarge)
            ) {
                // Login Logo
                AsyncImage(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(150.sdp)
                        .padding(top = AppTheme.dimens.paddingSmall),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(data = R.drawable.dasa_logo).crossfade(enable = true)
                        .scale(Scale.FILL).build(),
                    contentDescription = stringResource(id = R.string.login_heading_text)
                )

                // Heading Login
                TitleText(
                    modifier = Modifier.padding(top = AppTheme.dimens.paddingLarge),
                    text = stringResource(id = R.string.login_heading_text)
                )

                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    // Email or Mobile Number
                    EmailTextField(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = AppTheme.dimens.paddingLarge),
                        value = emailValue.value.text,
                        onValueChange = {
                            if(it.isEmpty()){
                                loginViewModel.updateEmailError(error = "Email is required")
                            }
                            emailValue.value = TextFieldValue(it)
                            loginViewModel.updateEmail(email = emailValue.value.text)
                        },
                        label = stringResource(id = R.string.login_email_id_or_phone_label),
                        isError = emailError.isNotEmpty(),
                        errorText = emailError
                    )


                    // Password
                    PasswordTextField(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = AppTheme.dimens.paddingLarge),
                        value = passwordValue.value.text,
                        onValueChange = {
                            if(it.isEmpty()){
                                loginViewModel.updatePasswordError(error = "Password is required")
                            }
                            passwordValue.value = TextFieldValue(it)
                            loginViewModel.updatePassword(password = it)
                        },
                        label = stringResource(id = R.string.login_password_label),
                        isError = passwordError.isNotEmpty(),
                        errorText = passwordError,
                        imeAction = ImeAction.Done
                    )

                    Text(
                        modifier = Modifier
                            .padding(top = AppTheme.dimens.paddingSmall)
                            .align(alignment = Alignment.End)
                            .clickable {

                            },
                        text = stringResource(id = R.string.forgot_password),
                        color = Color.Blue,
                        textAlign = TextAlign.End,
                        style = MaterialTheme.typography.bodyMedium,
                        textDecoration = TextDecoration.Underline
                    )

                    Spacer(modifier = Modifier.height(20.dp))
                    val context = LocalContext.current

                    when (loginResponse) {
                        is Loading -> {
                            CircularProgressIndicator(color = colorResource(id = R.color.purple_700))
                        }

                        else -> {
                            // Login Submit Button
                            NormalButton(modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = AppTheme.dimens.paddingSmall),
                                text = stringResource(id = R.string.login_button_text),
                                onClick = {
                                    if(emailValue.value.text.isEmpty()){
                                        loginViewModel.updateEmailError(error = "Email is required")
                                    }
                                    else if(passwordValue.value.text.isEmpty()){
                                        loginViewModel.updatePasswordError(error = "Password is required")
                                    }
                                    else {
                                        coroutineScope.launch(Dispatchers.IO) {
                                            loginViewModel.login(email = email, password = password)
                                                .collectLatest { state ->
                                                    when (state) {
                                                        is Success -> {
                                                            withContext(Dispatchers.Main) {
                                                                if (state().validate()) {
                                                                    loginViewModel.prefs.loginResponse =
                                                                        state()
                                                                    onLoginSuccess()
                                                                } else {
                                                                    val error =
                                                                        "You are not authorized to login"
                                                                    loginViewModel.updateLoginError(
                                                                        error = error
                                                                    )
                                                                    onLoginError(error)
                                                                }
                                                            }
                                                        }

                                                        is Fail -> {
                                                            withContext(Dispatchers.Main) {
                                                                val error =
                                                                    if (!isNetworkConnected(context)) "No internet connection" else "You are not authorized to login"
                                                                loginViewModel.updateLoginError(
                                                                    error = error
                                                                )
                                                                onLoginError(error)
                                                            }
                                                        }

                                                        else -> {
                                                        }
                                                    }
                                                }
                                        }
                                    }
                                })
                        }
                    }


                }

            }
        }
    }


}