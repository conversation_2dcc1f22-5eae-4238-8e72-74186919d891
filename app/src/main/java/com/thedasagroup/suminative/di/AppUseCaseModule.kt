package com.thedasagroup.suminative.di

import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.login.GetPOSSettingsUseCase
import com.thedasagroup.suminative.ui.login.GetStoreSettingsUseCase
import com.thedasagroup.suminative.ui.login.LoginUseCase
import com.thedasagroup.suminative.ui.orders.AcceptOrderWithDelayUseCase
import com.thedasagroup.suminative.ui.orders.ChangeStatusAndOrdersUseCase
import com.thedasagroup.suminative.ui.orders.ChangeStatusUseCase
import com.thedasagroup.suminative.ui.orders.CloseOpenStoreUseCase
import com.thedasagroup.suminative.ui.orders.GetAcceptedOrdersPagedUseCase
import com.thedasagroup.suminative.ui.orders.GetOrdersUseCase
import com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase
import com.thedasagroup.suminative.ui.orders.GetScheduleOrdersPagedUseCase
import com.thedasagroup.suminative.ui.orders.GetScheduleOrdersUseCase
import com.thedasagroup.suminative.ui.stock.ChangeStockUseCase
import com.thedasagroup.suminative.ui.stock.StockUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class) // Installs FooModule in the generate SingletonComponent.
internal object AppUseCaseModule {
    @Singleton
    @Provides
    fun providesLoginUseCase(loginRepository: LoginRepository): LoginUseCase {
        return LoginUseCase(loginRepository = loginRepository)
    }

    @Singleton
    @Provides
    fun providesGetOrdersUseCase(
        ordersRepository: OrdersRepository, prefs: Prefs
    ): GetOrdersUseCase {
        return GetOrdersUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesScheduleOrdersUseCase(
        ordersRepository: OrdersRepository, prefs: Prefs
    ): GetScheduleOrdersUseCase {
        return GetScheduleOrdersUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun provideChangeStatusUseCase(
        prefs: Prefs, ordersRepository: OrdersRepository
    ): ChangeStatusUseCase {
        return ChangeStatusUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesAcceptDeliveryOrderUseCase(
        prefs: Prefs, ordersRepository: OrdersRepository
    ): ChangeStatusAndOrdersUseCase {
        return ChangeStatusAndOrdersUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesAcceptOrderWithDelayUseCase(
        prefs: Prefs, ordersRepository: OrdersRepository, trueTimeImpl: TrueTimeImpl,
        getStoreSettingsUseCase: GetStoreSettingsUseCase
    ): AcceptOrderWithDelayUseCase {
        return AcceptOrderWithDelayUseCase(repo = ordersRepository, prefs = prefs, trueTimeImpl = trueTimeImpl,
            getStoreSettingsUseCase = getStoreSettingsUseCase)
    }

    @Singleton
    @Provides
    fun provideStoreSettingsUseCase(loginRepository: LoginRepository, prefs: Prefs): GetStoreSettingsUseCase {
        return GetStoreSettingsUseCase(loginRepository = loginRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun provideCloseOpenStoreUseCase(ordersRepository: OrdersRepository, prefs: Prefs): CloseOpenStoreUseCase {
        return CloseOpenStoreUseCase(ordersRepository = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providePendingOrdersUseCase(ordersRepository: OrdersRepository, prefs: Prefs): GetPendingOrdersPagedUseCase {
        return GetPendingOrdersPagedUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun provideScheduleOrdersPagedUseCase(ordersRepository: OrdersRepository, prefs: Prefs): GetScheduleOrdersPagedUseCase {
        return GetScheduleOrdersPagedUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesStockUseCase(stockRepository: StockRepository, prefs: Prefs): StockUseCase {
        return StockUseCase(stockRepository = stockRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesUpdateStockUseCase(stockRepository: StockRepository, prefs: Prefs): ChangeStockUseCase {
        return ChangeStockUseCase(stockRepository = stockRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesGetAcceptedOrderUseCase(ordersRepository: OrdersRepository, prefs: Prefs): GetAcceptedOrdersPagedUseCase {
        return GetAcceptedOrdersPagedUseCase(repo = ordersRepository, prefs = prefs)
    }

    @Singleton
    @Provides
    fun providesGetPosSettingsUsecase(loginRepository: LoginRepository, prefs: Prefs): GetPOSSettingsUseCase {
        return GetPOSSettingsUseCase(loginRepository = loginRepository, prefs = prefs)
    }
}

