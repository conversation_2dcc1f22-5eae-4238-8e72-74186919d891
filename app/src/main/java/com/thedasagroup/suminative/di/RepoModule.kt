package com.thedasagroup.suminative.di

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.SoundPool
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class) // Installs FooModule in the generate SingletonComponent.
internal object RepoModule {

    @Singleton
    @Provides
    fun providesSharedPrefs(@ApplicationContext context: Context): Prefs {
        return Prefs(context)
    }

    @Singleton
    @Provides
    fun providesLoginRepository(): LoginRepository {
        return LoginRepository()
    }

    @Singleton
    @Provides
    fun providesOrdersRepository(): OrdersRepository {
        return OrdersRepository()
    }

    @Provides
    @Singleton
    fun providesTrueTime() : TrueTimeImpl {
        return TrueTimeImpl()
    }

    @Provides
    @Singleton
    fun providesSoundPool() : SoundPool {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .build()

        return SoundPool.Builder()
            .setMaxStreams(1)
            .setAudioAttributes(audioAttributes)
            .build()
    }

    @Provides
    @Singleton
    fun providesSoundPoolPlayer(@ApplicationContext context: Context) : SoundPoolPlayer {
        return SoundPoolPlayer.create(context = context, resId = R.raw.notification)
    }

    @Provides
    @Singleton
    fun providesAudioManager(@ApplicationContext context: Context) : AudioManager {
        return context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    @Provides
    @Singleton
    fun providesStockRepository() : StockRepository {
        return StockRepository()
    }
}

