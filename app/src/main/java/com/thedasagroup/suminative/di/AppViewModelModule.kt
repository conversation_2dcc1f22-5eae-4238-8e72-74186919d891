package com.thedasagroup.suminative.di

import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.MavericksViewModelComponent
import com.airbnb.mvrx.hilt.ViewModelKey
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.stock.StockScreenViewModel
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.multibindings.IntoMap

@Module
@InstallIn(MavericksViewModelComponent::class)
interface AppViewModelModule {
    @Binds
    @IntoMap
    @ViewModelKey(LoginScreenViewModel::class)
    fun loginViewModelFactory(factory: LoginScreenViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(OrderScreenViewModel::class)
    fun orderScreenViewModelFactory(factory: OrderScreenViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(StockScreenViewModel::class)
    fun stockScreenViewModelFactory(factory: StockScreenViewModel.Factory): AssistedViewModelFactory<*, *>
}