<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/holo_red_dark"
    >

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="New Order"
        android:layout_centerHorizontal="true"
        android:textColor="@color/white"
        android:textSize="25sp"
        android:padding="@dimen/_10sdp"
        android:textStyle="bold"
        />

    <TextView
        android:id="@+id/tvMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tvTitle"
        android:text="You have 1 new order"
        android:textSize="20sp"
        android:layout_centerHorizontal="true"
        android:textColor="@color/white"
        />


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnOk"
        android:layout_width="@dimen/_100sdp"
        android:layout_height="@dimen/_50sdp"
        android:text="Ok"
        android:textSize="20sp"
        android:layout_below="@id/tvMessage"
        android:layout_alignParentEnd="true"
        android:padding="@dimen/_10sdp"
        android:layout_margin="@dimen/_10sdp"
        android:background="@color/white"
        android:textColor="@color/black"
        />

</RelativeLayout>